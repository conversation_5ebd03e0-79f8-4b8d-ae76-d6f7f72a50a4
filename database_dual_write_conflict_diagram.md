---
excalidraw-plugin: parsed
tags: [excalidraw]
---

==⚠ Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠==

# Excalidraw Data
## Text Elements
%%
## Drawing
```json
{
  "type": "excalidraw",
  "version": 2,
  "source": "https://github.com/zsviczian/obsidian-excalidraw-plugin",
  "elements": [
    {
      "id": "title",
      "type": "text",
      "x": 200,
      "y": 50,
      "width": 600,
      "height": 35,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 28,
      "fontFamily": 5,
      "text": "场景1：预迁移数据的UPDATE冲突",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "timeline",
      "type": "line",
      "x": 100,
      "y": 150,
      "width": 800,
      "height": 0,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [800, 0]]
    },
    {
      "id": "t0_marker",
      "type": "line",
      "x": 150,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t0_label",
      "type": "text",
      "x": 130,
      "y": 110,
      "width": 40,
      "height": 25,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "T0",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t1_marker",
      "type": "line",
      "x": 300,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t1_label",
      "type": "text",
      "x": 280,
      "y": 110,
      "width": 40,
      "height": 25,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "T1",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t2_marker",
      "type": "line",
      "x": 450,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t2_label",
      "type": "text",
      "x": 430,
      "y": 110,
      "width": 40,
      "height": 25,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "T2",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t3_marker",
      "type": "line",
      "x": 600,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t3_label",
      "type": "text",
      "x": 580,
      "y": 110,
      "width": 40,
      "height": 25,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "T3",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "t4_marker",
      "type": "line",
      "x": 750,
      "y": 140,
      "width": 0,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [0, 20]]
    },
    {
      "id": "t4_label",
      "type": "text",
      "x": 730,
      "y": 110,
      "width": 40,
      "height": 25,
      "angle": 0,
      "strokeColor": "#1e1e1e",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 20,
      "fontFamily": 5,
      "text": "T4",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "old_db_t0",
      "type": "rectangle",
      "x": 80,
      "y": 200,
      "width": 140,
      "height": 80,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "#a5d8ff",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "old_db_icon",
      "type": "text",
      "x": 90,
      "y": 210,
      "width": 120,
      "height": 20,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🗄️ 旧表",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "old_db_data",
      "type": "text",
      "x": 90,
      "y": 235,
      "width": 120,
      "height": 35,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "订单ID: 12345\n状态: 待处理",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "new_db_t0",
      "type": "rectangle",
      "x": 80,
      "y": 300,
      "width": 140,
      "height": 80,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "#ffd8a8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "new_db_icon",
      "type": "text",
      "x": 90,
      "y": 310,
      "width": 120,
      "height": 20,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "🗄️ 新表",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "new_db_empty",
      "type": "text",
      "x": 90,
      "y": 335,
      "width": 120,
      "height": 35,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "『空』\n无数据",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "historical_data_label",
      "type": "text",
      "x": 80,
      "y": 400,
      "width": 140,
      "height": 25,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 18,
      "fontFamily": 5,
      "text": "历史数据",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "dual_write_milestone",
      "type": "diamond",
      "x": 250,
      "y": 220,
      "width": 100,
      "height": 60,
      "angle": 0,
      "strokeColor": "#37b24d",
      "backgroundColor": "#c3fae8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "dual_write_text",
      "type": "text",
      "x": 260,
      "y": 240,
      "width": 80,
      "height": 40,
      "angle": 0,
      "strokeColor": "#37b24d",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "双写策略\n开始",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "update_request",
      "type": "rectangle",
      "x": 400,
      "y": 220,
      "width": 100,
      "height": 60,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "#e9ecef",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "update_request_text",
      "type": "text",
      "x": 410,
      "y": 240,
      "width": 80,
      "height": 40,
      "angle": 0,
      "strokeColor": "#495057",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "业务\nUPDATE请求",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "arrow_to_old",
      "type": "arrow",
      "x": 500,
      "y": 240,
      "width": 80,
      "height": -20,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [80, -20]],
      "lastCommittedPoint": [80, -20],
      "startBinding": null,
      "endBinding": null,
      "startArrowhead": null,
      "endArrowhead": "arrow"
    },
    {
      "id": "arrow_to_new",
      "type": "arrow",
      "x": 500,
      "y": 260,
      "width": 80,
      "height": 60,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "points": [[0, 0], [80, 60]],
      "lastCommittedPoint": [80, 60],
      "startBinding": null,
      "endBinding": null,
      "startArrowhead": null,
      "endArrowhead": "arrow"
    },
    {
      "id": "old_success",
      "type": "rectangle",
      "x": 550,
      "y": 200,
      "width": 140,
      "height": 80,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "#d3f9d8",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "old_success_text",
      "type": "text",
      "x": 560,
      "y": 210,
      "width": 120,
      "height": 60,
      "angle": 0,
      "strokeColor": "#51cf66",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "✅ UPDATE成功\n状态: 已完成\n影响行数: 1",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "new_failure",
      "type": "rectangle",
      "x": 550,
      "y": 300,
      "width": 140,
      "height": 80,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "#ffe0e6",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "new_failure_text",
      "type": "text",
      "x": 560,
      "y": 310,
      "width": 120,
      "height": 60,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "❌ UPDATE失败\n状态: 『未变更』\n影响行数: 0",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "final_inconsistency",
      "type": "rectangle",
      "x": 700,
      "y": 220,
      "width": 140,
      "height": 100,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "#ffe0e6",
      "fillStyle": "solid",
      "strokeWidth": 3,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100
    },
    {
      "id": "inconsistency_warning",
      "type": "text",
      "x": 710,
      "y": 230,
      "width": 120,
      "height": 80,
      "angle": 0,
      "strokeColor": "#ff6b6b",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 14,
      "fontFamily": 5,
      "text": "⚠️ 数据不一致\n旧表: 已完成\n新表: 『无记录』\n系统完整性受损",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "old_table_label",
      "type": "text",
      "x": 550,
      "y": 170,
      "width": 140,
      "height": 25,
      "angle": 0,
      "strokeColor": "#1971c2",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "旧表「生产环境」",
      "textAlign": "center",
      "verticalAlign": "middle"
    },
    {
      "id": "new_table_label",
      "type": "text",
      "x": 550,
      "y": 390,
      "width": 140,
      "height": 25,
      "angle": 0,
      "strokeColor": "#fd7e14",
      "backgroundColor": "transparent",
      "fillStyle": "solid",
      "strokeWidth": 2,
      "strokeStyle": "solid",
      "roughness": 1,
      "opacity": 100,
      "fontSize": 16,
      "fontFamily": 5,
      "text": "新表「分片」",
      "textAlign": "center",
      "verticalAlign": "middle"
    }
  ],
  "appState": {
    "gridSize": null,
    "viewBackgroundColor": "#ffffff"
  },
  "files": {}
}
```
%%
